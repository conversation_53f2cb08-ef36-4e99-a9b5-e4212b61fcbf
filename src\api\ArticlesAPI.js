import api from "../configs/api";

// Comment APIs for Articles
export const fetchBlogComments = (blogId) => {
  // Endpoint: GET /api/comment/blog/{blogId}
  return api.get(`/comment/blog/${blogId}`);
};

export const createComment = (commentData) => {
  // Endpoint: POST /api/comment
  // Request body: { blogId, description }
  return api.post("/comment", commentData);
};

export const deleteComment = (commentId) => {
  // Endpoint: DELETE /api/comment/{commentId}
  return api.delete(`/comment/${commentId}`);
};
