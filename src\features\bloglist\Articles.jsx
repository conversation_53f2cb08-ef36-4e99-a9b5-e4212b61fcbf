import React, { useState, useEffect } from "react";
import "./Articles.css";
import "./Comments.css";
import { Link } from "react-router-dom";
import { likeBlog } from "../../api/consultantAPI";
import {
  fetchBlogComments,
  createComment,
  deleteComment,
} from "../../api/ArticlesAPI";
import { API_BASE_URL } from "../../configs/serverConfig";

const Articles = () => {
  const [articles, setArticles] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [likingBlogs, setLikingBlogs] = useState(new Set());
  const articlesPerPage = 4;

  // Comment states
  const [showComments, setShowComments] = useState({});
  const [comments, setComments] = useState({});
  const [loadingComments, setLoadingComments] = useState({});
  const [newComment, setNewComment] = useState({});
  const [submittingComment, setSubmittingComment] = useState({});

  // User authentication state
  const [currentUser, setCurrentUser] = useState(null);

  // Check user authentication
  useEffect(() => {
    const token = localStorage.getItem("token");
    const userInfo = localStorage.getItem("userInfo");
    if (token && userInfo) {
      try {
        setCurrentUser(JSON.parse(userInfo));
      } catch (error) {
        console.error("Error parsing user info:", error);
      }
    }
  }, []);

  // Handle toggle comments
  const handleToggleComments = async (blogId) => {
    console.log("🔍 Toggle comments for blog:", blogId);
    const isCurrentlyShowing = showComments[blogId];
    console.log("📊 Currently showing:", isCurrentlyShowing);

    setShowComments((prev) => ({
      ...prev,
      [blogId]: !isCurrentlyShowing,
    }));

    // Load comments if showing for the first time
    if (!isCurrentlyShowing && !comments[blogId]) {
      console.log("📥 Loading comments for first time...");
      await loadComments(blogId);
    }
  };

  // Load comments for a blog
  const loadComments = async (blogId) => {
    setLoadingComments((prev) => ({ ...prev, [blogId]: true }));
    try {
      const response = await fetchBlogComments(blogId);
      setComments((prev) => ({
        ...prev,
        [blogId]: response.data || [],
      }));
    } catch (error) {
      console.error("Error loading comments:", error);
      setComments((prev) => ({
        ...prev,
        [blogId]: [],
      }));
    } finally {
      setLoadingComments((prev) => ({ ...prev, [blogId]: false }));
    }
  };

  // Handle submit comment
  const handleSubmitComment = async (blogId) => {
    const commentText = newComment[blogId]?.trim();
    if (!commentText || !currentUser) return;

    setSubmittingComment((prev) => ({ ...prev, [blogId]: true }));
    try {
      await createComment({
        blogId: parseInt(blogId),
        description: commentText,
      });

      // Clear input
      setNewComment((prev) => ({
        ...prev,
        [blogId]: "",
      }));

      // Reload comments
      await loadComments(blogId);
    } catch (error) {
      console.error("Error submitting comment:", error);
      alert("Không thể gửi bình luận. Vui lòng thử lại.");
    } finally {
      setSubmittingComment((prev) => ({ ...prev, [blogId]: false }));
    }
  };

  // Handle delete comment
  const handleDeleteComment = async (commentId, blogId) => {
    if (!confirm("Bạn có chắc chắn muốn xóa bình luận này?")) return;

    try {
      await deleteComment(commentId);
      // Reload comments
      await loadComments(blogId);
    } catch (error) {
      console.error("Error deleting comment:", error);
      alert("Không thể xóa bình luận. Vui lòng thử lại.");
    }
  };

  // Handle like blog
  const handleLikeBlog = async (e, blogId) => {
    e.preventDefault(); // Prevent navigation
    e.stopPropagation();

    if (likingBlogs.has(blogId)) return; // Prevent double clicking

    try {
      setLikingBlogs((prev) => new Set([...prev, blogId]));

      const response = await likeBlog(blogId);

      // Update local state optimistically
      setArticles((prevArticles) =>
        prevArticles.map((article) =>
          article.id === blogId
            ? { ...article, likeCount: (article.likeCount || 0) + 1 }
            : article
        )
      );

      // Reload all articles to get updated data from server
      setTimeout(async () => {
        try {
          // Reload the articles data from API
          const response = await fetch(`${API_BASE_URL}/blog?page=0&size=20`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          });

          if (response.ok) {
            const data = await response.json();
            let blogs = [];
            if (data?.content) {
              blogs = data.content;
            } else if (Array.isArray(data)) {
              blogs = data;
            }

            // Sort and transform like before
            const topBlogs = blogs
              .filter((blog) => blog.status === "PUBLISHED")
              .sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0))
              .slice(0, 5);

            const transformedArticles = topBlogs.map((blog, index) => ({
              id: blog.id,
              title: blog.title,
              excerpt: blog.content
                ? blog.content.substring(0, 150) + "..."
                : "Không có nội dung",
              image:
                blog.imgUrl ||
                "https://via.placeholder.com/400x300?text=No+Image",
              category: blog.tags?.[0]?.name || "general",
              author: {
                name: blog.author?.fullname || "Tác giả ẩn danh",
                avatar: blog.author?.imageUrl || "/placeholder.svg",
              },
              date: blog.createdAt
                ? new Date(blog.createdAt).toLocaleDateString("vi-VN")
                : "Không có ngày",
              viewCount: blog.viewCount || 0,
              likeCount: blog.likeCount || 0,
              featured: index === 0,
            }));

            setArticles(transformedArticles);
          }
        } catch (reloadError) {
          console.error(`Error reloading articles:`, reloadError);
        }
      }, 2000);
    } catch (error) {
      // Show user-friendly error message with login prompt
      const errorMessage =
        error.message || "Không thể thích bài viết. Vui lòng thử lại sau.";

      if (errorMessage.includes("đăng nhập")) {
        const shouldLogin = confirm(
          ` ${errorMessage}\n\n Bạn có muốn đăng nhập ngay không?`
        );
        if (shouldLogin) {
          // Redirect to login page
          window.location.href = "/login";
        }
      } else {
        alert(` ${errorMessage}`);
      }

      // Revert optimistic update on error
      setArticles((prevArticles) =>
        prevArticles.map((article) =>
          article.id === blogId
            ? {
                ...article,
                likeCount: Math.max(0, (article.likeCount || 0) - 1),
              }
            : article
        )
      );
    } finally {
      setLikingBlogs((prev) => {
        const newSet = new Set(prev);
        newSet.delete(blogId);
        return newSet;
      });
    }
  };

  useEffect(() => {
    const loadTopBlogs = async () => {
      try {
        setLoading(true);
        // Lấy nhiều blogs để có thể sort theo viewCount
        // Gọi API trực tiếp không qua api instance để tránh CORS
        const response = await fetch(`${API_BASE_URL}/blog?page=0&size=20`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            // Không gửi Authorization header để tránh CORS preflight
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        let blogs = [];
        if (data?.content) {
          blogs = data.content;
        } else if (Array.isArray(data)) {
          blogs = data;
        }

        // Sort theo viewCount giảm dần và lấy top 5
        const topBlogs = blogs
          .filter((blog) => blog.status === "PUBLISHED") // Chỉ lấy blog đã publish
          .sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0))
          .slice(0, 5);

        // Transform data để phù hợp với UI
        const transformedArticles = topBlogs.map((blog, index) => ({
          id: blog.id,
          title: blog.title,
          excerpt: blog.content
            ? blog.content.substring(0, 150) + "..."
            : "Không có nội dung",
          image:
            blog.imgUrl || "https://via.placeholder.com/400x300?text=No+Image",
          category: blog.tags?.[0]?.name || "general",
          author: {
            name: blog.author?.fullname || "Tác giả ẩn danh",
            avatar: blog.author?.imageUrl || "/placeholder.svg",
          },
          date: blog.createdAt
            ? new Date(blog.createdAt).toLocaleDateString("vi-VN")
            : "Không có ngày",
          viewCount: blog.viewCount || 0,
          likeCount: blog.likeCount || 0,
          featured: index === 0, // Blog có lượt xem cao nhất làm featured
        }));

        setArticles(transformedArticles);

        // Lưu vào localStorage để dùng ở BlogDetail
        localStorage.setItem(
          "allArticles",
          JSON.stringify(transformedArticles)
        );
      } catch (error) {
        // Fallback to empty array if API fails
        setArticles([]);
      } finally {
        setLoading(false);
      }
    };

    loadTopBlogs();
  }, []);

  const featuredArticle = articles.find((article) => article.featured);
  const sidebarArticles = featuredArticle
    ? articles.filter((a) => a.id !== featuredArticle.id)
    : articles;

  const indexOfLastArticle = currentPage * articlesPerPage;
  const indexOfFirstArticle = indexOfLastArticle - articlesPerPage;
  const currentSidebarArticles = sidebarArticles.slice(
    indexOfFirstArticle,
    indexOfLastArticle
  );

  const totalPages = Math.ceil(sidebarArticles.length / articlesPerPage);

  if (loading) {
    return (
      <section className="articles section">
        <div className="container">
          <div
            className="loading-container"
            style={{ textAlign: "center", padding: "50px" }}
          >
            <div> Đang tải top 5 bài viết có lượt xem cao nhất...</div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="articles section">
      <div className="container">
        <div className="blog-layout">
          {/* Featured Article */}
          {featuredArticle && (
            <div className="featured-article-container">
              <Link
                to={`/blog/${featuredArticle.id}`}
                className="featured-article"
              >
                <div className="featured-image">
                  <img
                    src={featuredArticle.image}
                    alt={featuredArticle.title}
                  />
                </div>
                <div className="featured-content">
                  <h2>{featuredArticle.title}</h2>
                  <p>{featuredArticle.excerpt}</p>
                  <div className="article-meta">
                    <div className="article-author">
                      <img
                        src={featuredArticle.author.avatar}
                        alt={featuredArticle.author.name}
                      />
                      <span>{featuredArticle.author.name}</span>
                    </div>
                    <div className="article-date">{featuredArticle.date}</div>
                  </div>
                </div>
              </Link>

              {/* Article Stats - Outside Link */}
              <div className="article-stats">
                <div className="stat-item">
                  <span className="stat-icon">👁️</span>
                  <span className="stat-count">
                    {featuredArticle.viewCount || 0}
                  </span>
                </div>
                <button
                  className={`stat-item like-button ${
                    likingBlogs.has(featuredArticle.id) ? "liking" : ""
                  }`}
                  onClick={(e) => handleLikeBlog(e, featuredArticle.id)}
                  disabled={likingBlogs.has(featuredArticle.id)}
                >
                  <span className="stat-icon">❤️</span>
                  <span className="stat-count">
                    {featuredArticle.likeCount || 0}
                  </span>
                </button>
                <button
                  className="stat-item comment-button"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log(
                      "💬 Comment button clicked for:",
                      featuredArticle.id
                    );
                    handleToggleComments(featuredArticle.id);
                  }}
                >
                  <span className="stat-icon">💬</span>
                  <span className="stat-count">
                    {comments[featuredArticle.id]?.length || 0}
                  </span>
                </button>
              </div>
            </div>
          )}

          {/* Featured Article Comments */}
          {featuredArticle && showComments[featuredArticle.id] && (
            <div className="featured-comments-section">
              <div className="comments-header">
                <h3>Bình luận</h3>
                <button
                  className="close-comments-btn"
                  onClick={() =>
                    setShowComments((prev) => ({
                      ...prev,
                      [featuredArticle.id]: false,
                    }))
                  }
                >
                  ✕
                </button>
              </div>

              {/* Comment Input - Only for logged in users */}
              {currentUser && (
                <div className="comment-input-section">
                  <div className="comment-input-header">
                    <img
                      src={currentUser.imageUrl || "/placeholder.svg"}
                      alt={currentUser.fullname}
                      className="commenter-avatar"
                    />
                    <span className="commenter-name">
                      {currentUser.fullname}
                    </span>
                  </div>
                  <textarea
                    value={newComment[featuredArticle.id] || ""}
                    onChange={(e) =>
                      setNewComment((prev) => ({
                        ...prev,
                        [featuredArticle.id]: e.target.value,
                      }))
                    }
                    placeholder="Viết bình luận của bạn..."
                    className="comment-textarea"
                    rows={3}
                  />
                  <button
                    onClick={() => handleSubmitComment(featuredArticle.id)}
                    disabled={
                      submittingComment[featuredArticle.id] ||
                      !newComment[featuredArticle.id]?.trim()
                    }
                    className="submit-comment-btn"
                  >
                    {submittingComment[featuredArticle.id]
                      ? "Đang gửi..."
                      : "Gửi bình luận"}
                  </button>
                </div>
              )}

              {/* Login prompt for non-logged users */}
              {!currentUser && (
                <div className="login-prompt">
                  <p>
                    Vui lòng <Link to="/login">đăng nhập</Link> để bình luận
                  </p>
                </div>
              )}

              {/* Comments List */}
              <div className="comments-list">
                {loadingComments[featuredArticle.id] ? (
                  <div className="loading-comments">Đang tải bình luận...</div>
                ) : comments[featuredArticle.id]?.length > 0 ? (
                  comments[featuredArticle.id].map((comment) => (
                    <div key={comment.id} className="comment-item">
                      <div className="comment-header">
                        <span className="comment-author">
                          {comment.commenterName}
                        </span>
                        <span className="comment-date">
                          {new Date(comment.createAt).toLocaleString("vi-VN")}
                        </span>
                        {currentUser &&
                          currentUser.fullname === comment.commenterName && (
                            <button
                              onClick={() =>
                                handleDeleteComment(
                                  comment.id,
                                  featuredArticle.id
                                )
                              }
                              className="delete-comment-btn"
                              title="Xóa bình luận"
                            >
                              🗑️
                            </button>
                          )}
                      </div>
                      <div className="comment-content">
                        {comment.description}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="no-comments">Chưa có bình luận nào</div>
                )}
              </div>
            </div>
          )}

          {/* Sidebar Articles */}
          <div className="sidebar-articles">
            {currentSidebarArticles.map((article) => (
              <div key={article.id} className="sidebar-article-container">
                <Link to={`/blog/${article.id}`} className="sidebar-article">
                  <div className="sidebar-article-image">
                    <img src={article.image} alt={article.title} />
                  </div>
                  <div className="sidebar-article-content">
                    <h3>{article.title}</h3>
                    <div className="article-meta">
                      <div className="article-author">
                        <img
                          src={article.author.avatar}
                          alt={article.author.name}
                        />
                        <span>{article.author.name}</span>
                      </div>
                      <div className="article-date">{article.date}</div>
                    </div>
                  </div>
                </Link>

                {/* Sidebar Article Stats - Outside Link */}
                <div className="sidebar-article-stats">
                  <div className="stat-item">
                    <span className="stat-icon">👁️</span>
                    <span className="stat-count">{article.viewCount || 0}</span>
                  </div>
                  <button
                    className={`stat-item like-button ${
                      likingBlogs.has(article.id) ? "liking" : ""
                    }`}
                    onClick={(e) => handleLikeBlog(e, article.id)}
                    disabled={likingBlogs.has(article.id)}
                  >
                    <span className="stat-icon">❤️</span>
                    <span className="stat-count">{article.likeCount || 0}</span>
                  </button>
                  <button
                    className="stat-item comment-button"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log(
                        "💬 Sidebar comment button clicked for:",
                        article.id
                      );
                      handleToggleComments(article.id);
                    }}
                  >
                    <span className="stat-icon">💬</span>
                    <span className="stat-count">
                      {comments[article.id]?.length || 0}
                    </span>
                  </button>
                </div>

                {/* Sidebar Article Comments */}
                {showComments[article.id] && (
                  <div className="sidebar-comments-section">
                    <div className="comments-header">
                      <h4>Bình luận</h4>
                      <button
                        className="close-comments-btn"
                        onClick={() =>
                          setShowComments((prev) => ({
                            ...prev,
                            [article.id]: false,
                          }))
                        }
                      >
                        ✕
                      </button>
                    </div>

                    {/* Comment Input - Only for logged in users */}
                    {currentUser && (
                      <div className="comment-input-section">
                        <textarea
                          value={newComment[article.id] || ""}
                          onChange={(e) =>
                            setNewComment((prev) => ({
                              ...prev,
                              [article.id]: e.target.value,
                            }))
                          }
                          placeholder="Viết bình luận..."
                          className="comment-textarea"
                          rows={2}
                        />
                        <button
                          onClick={() => handleSubmitComment(article.id)}
                          disabled={
                            submittingComment[article.id] ||
                            !newComment[article.id]?.trim()
                          }
                          className="submit-comment-btn"
                        >
                          {submittingComment[article.id]
                            ? "Đang gửi..."
                            : "Gửi"}
                        </button>
                      </div>
                    )}

                    {/* Login prompt for non-logged users */}
                    {!currentUser && (
                      <div className="login-prompt">
                        <p>
                          <Link to="/login">Đăng nhập</Link> để bình luận
                        </p>
                      </div>
                    )}

                    {/* Comments List */}
                    <div className="comments-list">
                      {loadingComments[article.id] ? (
                        <div className="loading-comments">Đang tải...</div>
                      ) : comments[article.id]?.length > 0 ? (
                        comments[article.id].map((comment) => (
                          <div key={comment.id} className="comment-item">
                            <div className="comment-header">
                              <span className="comment-author">
                                {comment.commenterName}
                              </span>
                              <span className="comment-date">
                                {new Date(comment.createAt).toLocaleString(
                                  "vi-VN"
                                )}
                              </span>
                              {currentUser &&
                                currentUser.fullname ===
                                  comment.commenterName && (
                                  <button
                                    onClick={() =>
                                      handleDeleteComment(
                                        comment.id,
                                        article.id
                                      )
                                    }
                                    className="delete-comment-btn"
                                    title="Xóa bình luận"
                                  >
                                    🗑️
                                  </button>
                                )}
                            </div>
                            <div className="comment-content">
                              {comment.description}
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="no-comments">Chưa có bình luận</div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="sidebar-pagination">
                {[...Array(totalPages)].map((_, index) => (
                  <button
                    key={index}
                    className={`pagination-dot ${
                      currentPage === index + 1 ? "active" : ""
                    }`}
                    onClick={() => setCurrentPage(index + 1)}
                  ></button>
                ))}
              </div>
            )}

            <div className="see-all-button-blog">
              <Link to="/blog" className="view-all-link-blog">
                Xem tất cả bài viết
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Articles;
