/* Comment System Styles */

.featured-article-container {
  display: flex;
  flex-direction: column;
}

.featured-article-container .article-stats {
  margin-top: 10px;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  display: flex;
  gap: 15px;
  align-items: center;
}

.sidebar-article-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

.sidebar-article-container .sidebar-article-stats {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  display: flex;
  gap: 12px;
  align-items: center;
  font-size: 0.9rem;
}

.sidebar-comments-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
  border: 1px solid #e9ecef;
  max-height: 300px;
  overflow-y: auto;
}

.sidebar-comments-section .comments-header h4 {
  margin: 0;
  color: #495057;
  font-size: 1rem;
  font-weight: 600;
}

.sidebar-comments-section .comment-textarea {
  min-height: 60px;
  font-size: 0.85rem;
}

.sidebar-comments-section .comment-item {
  padding: 10px;
  margin-bottom: 8px;
}

.sidebar-comments-section .comment-content {
  font-size: 0.85rem;
}

.comment-button {
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  z-index: 10;
  position: relative;
}

.comment-button:hover {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.featured-comments-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
  border: 1px solid #e9ecef;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #dee2e6;
}

.comments-header h3 {
  margin: 0;
  color: #495057;
  font-size: 1.2rem;
  font-weight: 600;
}

.close-comments-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #6c757d;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-comments-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.comment-input-section {
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid #dee2e6;
}

.comment-input-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.commenter-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 10px;
  object-fit: cover;
}

.commenter-name {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.comment-textarea {
  width: 100%;
  border: 1px solid #ced4da;
  border-radius: 6px;
  padding: 10px;
  font-family: inherit;
  font-size: 0.9rem;
  resize: vertical;
  min-height: 80px;
  margin-bottom: 10px;
}

.comment-textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.submit-comment-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.submit-comment-btn:hover:not(:disabled) {
  background: #0056b3;
}

.submit-comment-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.login-prompt {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  margin-bottom: 20px;
}

.login-prompt p {
  margin: 0;
  color: #6c757d;
}

.login-prompt a {
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
}

.login-prompt a:hover {
  text-decoration: underline;
}

.comments-list {
  max-height: 400px;
  overflow-y: auto;
}

.loading-comments {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  font-style: italic;
}

.comment-item {
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

.comment-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.comment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.comment-author {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.comment-date {
  color: #6c757d;
  font-size: 0.8rem;
}

.delete-comment-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.delete-comment-btn:hover {
  background: #f8d7da;
}

.comment-content {
  color: #495057;
  line-height: 1.5;
  font-size: 0.9rem;
  word-wrap: break-word;
}

.no-comments {
  text-align: center;
  padding: 30px;
  color: #6c757d;
  font-style: italic;
  background: white;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .featured-comments-section {
    padding: 15px;
    margin-top: 15px;
  }

  .comment-input-section {
    padding: 12px;
  }

  .comment-textarea {
    min-height: 60px;
  }

  .comment-item {
    padding: 12px;
  }

  .comments-header h3 {
    font-size: 1.1rem;
  }
}
